package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table

/**
 * 代理流程日志数据结构
 * 用于记录"/sse/n/run/flow"接口的完整请求和响应数据
 */
@Serializable
data class AgentFlowLog(
    val id: Long = 0,                           // 唯一ID
    val userId: String,                         // JWT中的用户名
    val requestId: String,                      // 请求ID
    val timestamp: Long = System.currentTimeMillis(), // 时间戳（毫秒）
    val username: String,                       // 用户名
    val dataId: String,                         // 数据ID
    val fullResponse: String,                   // 接口返回的所有数据
    val permitMeeData: String,                  // permit_mee_data JSON字符串
    val agentList: String,                      // agent_list JSON字符串
    val requestParams: String? = null,          // 完整请求参数 JSON字符串
    val status: String = "processing",          // 处理状态：processing, completed, failed
    val errorMessage: String? = null,           // 错误信息（如果有）
    val processingTimeMs: Long? = null,         // 处理时间（毫秒）
    val agentCount: Int = 0,                    // 代理数量
    val completedAgentCount: Int = 0,           // 已完成代理数量
    val failedAgentCount: Int = 0,              // 失败代理数量
    
    // 审计字段
    val createBy: String? = null,               // 创建人
    val updateBy: String? = null,               // 更新人
    val createdAt: Long = System.currentTimeMillis(), // 创建时间戳
    val updatedAt: Long = System.currentTimeMillis()  // 更新时间戳
)

/**
 * 代理流程日志表
 * 用于记录和追踪代理流程的完整执行情况
 */
object AgentFlowLogs : Table("agent_flow_log") {
    val id = long("id").autoIncrement()
    val userId = varchar("user_id", length = 64)
    val requestId = varchar("request_id", length = 64)
    val timestamp = long("timestamp")
    val username = varchar("username", length = 64)
    val dataId = varchar("data_id", length = 64)
    val fullResponse = text("full_response")
    val permitMeeData = text("permit_mee_data")
    val agentList = text("agent_list")
    val requestParams = text("request_params").nullable()
    val status = varchar("status", length = 32)
    val errorMessage = text("error_message").nullable()
    val processingTimeMs = long("processing_time_ms").nullable()
    val agentCount = integer("agent_count")
    val completedAgentCount = integer("completed_agent_count")
    val failedAgentCount = integer("failed_agent_count")
    
    // 审计字段
    val createBy = varchar("create_by", length = 64).nullable()
    val updateBy = varchar("update_by", length = 64).nullable()
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")

    override val primaryKey = PrimaryKey(id, name = "PK_agent_flow_log_id")
}
