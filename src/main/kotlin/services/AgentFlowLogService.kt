package com.dzhp.permit.services

import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.AgentFlowLog
import com.dzhp.permit.models.AgentFlowLogs
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory

/**
 * 代理流程日志服务
 * 负责记录和管理代理流程的完整执行日志
 */
class AgentFlowLogService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()

    /**
     * 创建新的代理流程日志记录
     *
     * @param log 代理流程日志对象
     * @return 数据库生成的主键ID
     */
    fun createLog(log: AgentFlowLog): Long =
        transaction(db) {
            try {
                AgentFlowLogs.insert {
                    it[userId] = log.userId
                    it[requestId] = log.requestId
                    it[timestamp] = log.timestamp
                    it[username] = log.username
                    it[dataId] = log.dataId
                    it[fullResponse] = log.fullResponse
                    it[permitMeeData] = log.permitMeeData
                    it[agentList] = log.agentList
                    it[requestParams] = log.requestParams
                    it[status] = log.status
                    it[errorMessage] = log.errorMessage
                    it[processingTimeMs] = log.processingTimeMs
                    it[agentCount] = log.agentCount
                    it[completedAgentCount] = log.completedAgentCount
                    it[failedAgentCount] = log.failedAgentCount
                    it[createBy] = log.createBy
                    it[updateBy] = log.updateBy
                    it[createdAt] = log.createdAt
                    it[updatedAt] = log.updatedAt
                } get AgentFlowLogs.id
            } catch (e: Exception) {
                logger.error("创建代理流程日志失败: ${e.message}", e)
                throw e
            }
        }

    /**
     * 更新代理流程日志记录
     *
     * @param requestId 请求ID
     * @param updates 更新的字段映射
     * @return 更新的记录数
     */
    fun updateLogByRequestId(requestId: String, updates: Map<String, Any?>): Int =
        transaction(db) {
            try {
                AgentFlowLogs.update({ AgentFlowLogs.requestId eq requestId }) {
                    updates.forEach { (key, value) ->
                        when (key) {
                            "status" -> it[status] = value as String
                            "errorMessage" -> it[errorMessage] = value as String?
                            "processingTimeMs" -> it[processingTimeMs] = value as Long?
                            "completedAgentCount" -> it[completedAgentCount] = value as Int
                            "failedAgentCount" -> it[failedAgentCount] = value as Int
                            "fullResponse" -> it[fullResponse] = value as String
                            "updateBy" -> it[updateBy] = value as String?
                        }
                    }
                    it[updatedAt] = System.currentTimeMillis()
                }
            } catch (e: Exception) {
                logger.error("更新代理流程日志失败: requestId=$requestId, ${e.message}", e)
                throw e
            }
        }

    /**
     * 根据请求ID查询日志记录
     *
     * @param requestId 请求ID
     * @return 日志记录或null
     */
    fun getLogByRequestId(requestId: String): AgentFlowLog? =
        transaction(db) {
            try {
                AgentFlowLogs.select { AgentFlowLogs.requestId eq requestId }
                    .singleOrNull()
                    ?.let { row ->
                        AgentFlowLog(
                            id = row[AgentFlowLogs.id],
                            userId = row[AgentFlowLogs.userId],
                            requestId = row[AgentFlowLogs.requestId],
                            timestamp = row[AgentFlowLogs.timestamp],
                            username = row[AgentFlowLogs.username],
                            dataId = row[AgentFlowLogs.dataId],
                            fullResponse = row[AgentFlowLogs.fullResponse],
                            permitMeeData = row[AgentFlowLogs.permitMeeData],
                            agentList = row[AgentFlowLogs.agentList],
                            requestParams = row[AgentFlowLogs.requestParams],
                            status = row[AgentFlowLogs.status],
                            errorMessage = row[AgentFlowLogs.errorMessage],
                            processingTimeMs = row[AgentFlowLogs.processingTimeMs],
                            agentCount = row[AgentFlowLogs.agentCount],
                            completedAgentCount = row[AgentFlowLogs.completedAgentCount],
                            failedAgentCount = row[AgentFlowLogs.failedAgentCount],
                            createBy = row[AgentFlowLogs.createBy],
                            updateBy = row[AgentFlowLogs.updateBy],
                            createdAt = row[AgentFlowLogs.createdAt],
                            updatedAt = row[AgentFlowLogs.updatedAt]
                        )
                    }
            } catch (e: Exception) {
                logger.error("查询代理流程日志失败: requestId=$requestId, ${e.message}", e)
                null
            }
        }

    /**
     * 根据用户ID查询日志记录列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 日志记录列表
     */
    fun getLogsByUserId(userId: String, limit: Int = 50, offset: Long = 0): List<AgentFlowLog> =
        transaction(db) {
            try {
                AgentFlowLogs.select { AgentFlowLogs.userId eq userId }
                    .orderBy(AgentFlowLogs.timestamp, SortOrder.DESC)
                    .limit(limit, offset)
                    .map { row ->
                        AgentFlowLog(
                            id = row[AgentFlowLogs.id],
                            userId = row[AgentFlowLogs.userId],
                            requestId = row[AgentFlowLogs.requestId],
                            timestamp = row[AgentFlowLogs.timestamp],
                            username = row[AgentFlowLogs.username],
                            dataId = row[AgentFlowLogs.dataId],
                            fullResponse = row[AgentFlowLogs.fullResponse],
                            permitMeeData = row[AgentFlowLogs.permitMeeData],
                            agentList = row[AgentFlowLogs.agentList],
                            requestParams = row[AgentFlowLogs.requestParams],
                            status = row[AgentFlowLogs.status],
                            errorMessage = row[AgentFlowLogs.errorMessage],
                            processingTimeMs = row[AgentFlowLogs.processingTimeMs],
                            agentCount = row[AgentFlowLogs.agentCount],
                            completedAgentCount = row[AgentFlowLogs.completedAgentCount],
                            failedAgentCount = row[AgentFlowLogs.failedAgentCount],
                            createBy = row[AgentFlowLogs.createBy],
                            updateBy = row[AgentFlowLogs.updateBy],
                            createdAt = row[AgentFlowLogs.createdAt],
                            updatedAt = row[AgentFlowLogs.updatedAt]
                        )
                    }
            } catch (e: Exception) {
                logger.error("查询用户代理流程日志失败: userId=$userId, ${e.message}", e)
                emptyList()
            }
        }
}
