### 搜索许可证MEE响应列表
GET http://localhost:8080/api/search/permit/mee/response/list?username=test_user
Authorization: Bearer {{user_jwt_token}}

### 搜索许可证MEE响应详情
GET http://localhost:8080/api/search/permit/mee/response/content?username=test_user&data_id=test_data_id&timestamp=1234567890
Authorization: Bearer {{user_jwt_token}}

### 测试无效的JWT令牌 - 搜索许可证MEE响应列表
GET http://localhost:8080/api/search/permit/mee/response/list?username=test_user
Authorization: Bearer invalid_token

### 测试缺少参数 - 搜索许可证MEE响应列表
GET http://localhost:8080/api/search/permit/mee/response/list
Authorization: Bearer {{user_jwt_token}}

### 测试缺少参数 - 搜索许可证MEE响应详情（缺少data_id和timestamp）
GET http://localhost:8080/api/search/permit/mee/response/content?username=test_user
Authorization: Bearer {{user_jwt_token}}

### 测试缺少参数 - 搜索许可证MEE响应详情（缺少timestamp）
GET http://localhost:8080/api/search/permit/mee/response/content?username=test_user&data_id=test_data_id
Authorization: Bearer {{user_jwt_token}}
